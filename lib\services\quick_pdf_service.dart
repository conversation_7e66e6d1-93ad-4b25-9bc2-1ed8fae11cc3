import 'dart:math' as math;
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:image/image.dart' as img;
import '../models/user_profile_model.dart';
import '../models/echo_enums.dart';
import '../models/interpretation_section.dart';
import '../services/user_profile_service.dart';
import '../constants/font_sizes.dart';

class PdfGenerationData {
  final UserProfile userProfile;
  final Map<String, dynamic>? patientData;
  final List<InterpretationSection> interpretationSections;
  final Uint8List logoImageData;
  final Uint8List? backgroundImageData;
  final Map<String, dynamic> positionedBackgrounds;
  final Uint8List notoFontData;
  final Uint8List hlaImageData;
  final Uint8List vlaImageData;

  PdfGenerationData({
    required this.userProfile,
    this.patientData,
    required this.interpretationSections,
    required this.logoImageData,
    required this.backgroundImageData,
    required this.positionedBackgrounds,
    required this.notoFontData,
    required this.hlaImageData,
    required this.vlaImageData,
  });
}

class QuickPdfService {
  static double pdfFontSize(double appFontSize) {
    return appFontSize * 0.85;
  }

  static Map<String, String> _getGenderSpecificNormalRanges(String? gender) {
    final bool isMale = gender?.toLowerCase() == 'male';

    return {
      'lvedd': isMale ? '(42-58)' : '(38-52)',
      'lvesd': isMale ? '(25-40)' : '(22-35)',
      'ivsd': isMale ? '(6-11)' : '(6-11)',
      'lvpw': isMale ? '(6-10)' : '(6-10)',
      'la': isMale ? '(30-40)' : '(27-38)',
      'ao': '(20-40)',
      'ef': isMale ? '(52-72)' : '(54-74)',
      'fs': '(25-45)',
    };
  }

  static PdfColor hexToPdfColor(String hexColor) {
    try {
      final String cleanHex =
          hexColor.startsWith('#') ? hexColor.substring(1) : hexColor;

      debugPrint('Converting hex color: $hexColor, cleaned: $cleanHex');

      if (cleanHex.length == 6) {
        final int r = int.parse(cleanHex.substring(0, 2), radix: 16);
        final int g = int.parse(cleanHex.substring(2, 4), radix: 16);
        final int b = int.parse(cleanHex.substring(4, 6), radix: 16);
        debugPrint('Parsed RGB: r=$r, g=$g, b=$b');
        return PdfColor(r, g, b);
      } else if (cleanHex.length == 3) {
        final int r = int.parse(
          cleanHex.substring(0, 1) + cleanHex.substring(0, 1),
          radix: 16,
        );
        final int g = int.parse(
          cleanHex.substring(1, 2) + cleanHex.substring(1, 2),
          radix: 16,
        );
        final int b = int.parse(
          cleanHex.substring(2, 3) + cleanHex.substring(2, 3),
          radix: 16,
        );
        debugPrint('Parsed RGB (short hex): r=$r, g=$g, b=$b');
        return PdfColor(r, g, b);
      }
    } catch (e) {
      debugPrint('Error parsing hex color: $e');
    }

    debugPrint('Returning default black color');
    return PdfColor(0, 0, 0);
  }

  static Future<Uint8List> generateQuickReport({
    Map<String, dynamic>? patientData,
    required List<InterpretationSection> interpretationSections,
  }) async {
    final UserProfile userProfile = await UserProfileService.getUserProfile();

    final Uint8List logoImageData = await _getLogoImage(userProfile);
    final Uint8List? backgroundImageData = await _getBackgroundImage(
      userProfile,
    );
    final Map<String, dynamic> positionedBackgrounds =
        await _getPositionedBackgroundImages(userProfile);
    final Uint8List notoFontData = await _getArabicFont();
    final Uint8List hlaImageData = await _getHLAImage();
    final Uint8List vlaImageData = await _getVLAImage();

    final pdfData = PdfGenerationData(
      userProfile: userProfile,
      patientData: patientData,
      interpretationSections: interpretationSections,
      logoImageData: logoImageData,
      backgroundImageData: backgroundImageData,
      positionedBackgrounds: positionedBackgrounds,
      notoFontData: notoFontData,
      hlaImageData: hlaImageData,
      vlaImageData: vlaImageData,
    );

    return compute(_generatePdfInBackground, pdfData);
  }

  static Uint8List _generatePdfInBackground(PdfGenerationData data) {
    final UserProfile userProfile = data.userProfile;
    final Map<String, dynamic>? patientData = data.patientData;
    final List<InterpretationSection> interpretationSections =
        data.interpretationSections;
    final Uint8List logoImageData = data.logoImageData;
    final Uint8List? backgroundImageData = data.backgroundImageData;
    final Map<String, dynamic> positionedBackgrounds =
        data.positionedBackgrounds;
    final Uint8List notoFontData = data.notoFontData;
    final Uint8List hlaImageData = data.hlaImageData;

    Map<LVSegment, WallMotionScore> wallMotionScores = {};

    for (final segment in LVSegment.values) {
      wallMotionScores[segment] = WallMotionScore.normal;
    }

    if (patientData != null &&
        patientData['wallMotion'] != null &&
        patientData['wallMotion']['abnormality'] == 'yes') {
      try {
        if (patientData['wallMotion']['abnormalityType'] == 'global') {
          for (final segment in LVSegment.values) {
            wallMotionScores[segment] = WallMotionScore.hypokinetic;
          }
        } else {
          patientData['wallMotion'].forEach((key, value) {
            if (key != 'abnormality' && key != 'abnormalityType') {
              LVSegment? matchedSegment;

              String segmentKey = key;
              try {
                segmentKey = key.replaceAll(' ', '').toLowerCase();

                if (segmentKey == 'apicalseptal') {
                  segmentKey = 'apicalseptal';
                } else if (segmentKey == 'basalanteroseptal') {
                  segmentKey = 'basalanteroseptal';
                } else if (segmentKey == 'basalinferoseptal') {
                  segmentKey = 'basalinferoseptal';
                } else if (segmentKey == 'midinferoseptal') {
                  segmentKey = 'midinferoseptal';
                } else if (segmentKey == 'midanteroseptal') {
                  segmentKey = 'midanteroseptal';
                }
              } catch (e) {
                debugPrint('Error converting segment key: $e');
                segmentKey = key.toLowerCase();
              }

              for (final segment in LVSegment.values) {
                final String enumName = segment.toString().split('.').last;
                if (enumName.toLowerCase() == segmentKey.toLowerCase()) {
                  matchedSegment = segment;
                  break;
                }
              }

              if (matchedSegment == null) {
                for (final segment in LVSegment.values) {
                  final String displayName =
                      segment.displayName.replaceAll(' ', '').toLowerCase();
                  if (displayName == segmentKey.toLowerCase()) {
                    matchedSegment = segment;
                    break;
                  }
                }
              }

              if (matchedSegment != null) {
                WallMotionScore score = WallMotionScore.normal;
                switch (value.toString().toLowerCase()) {
                  case 'hypokinetic':
                    score = WallMotionScore.hypokinetic;
                    break;
                  case 'akinetic':
                    score = WallMotionScore.akinetic;
                    break;
                  case 'dyskinetic':
                    score = WallMotionScore.dyskinetic;
                    break;
                  case 'aneurysmal':
                    score = WallMotionScore.aneurysmal;
                    break;
                }
                wallMotionScores[matchedSegment] = score;
                debugPrint(
                  'Matched segment: ${matchedSegment.toString()} with score: ${score.toString()}',
                );
              } else {
                debugPrint(
                  'Failed to match segment key: $key (converted to: $segmentKey)',
                );
              }
            }
          });
        }
      } catch (e) {
        debugPrint('Error processing wall motion data: $e');
      }
    }

    final PdfDocument document = PdfDocument();

    document.pageSettings.margins =
        PdfMargins()
          ..left = 5
          ..right = 5
          ..top = 5
          ..bottom = 5;

    final PdfPage page = document.pages.add();
    final Size pageSize = page.getClientSize();
    final double patientTableY = 110;

    final double patientGridMargin = 5;

    final double patientDataHeight = 16 + (4 * 18);

    if (positionedBackgrounds.containsKey('patient-data')) {
      try {
        final dynamic background = positionedBackgrounds['patient-data'];

        if (background['type'] == 'color') {
          final String colorHex = background['color'] as String;
          final double alpha = background['alpha'] as double;

          final PdfColor pdfColor = hexToPdfColor(colorHex);

          page.graphics.save();

          page.graphics.setTransparency(alpha);

          page.graphics.drawRectangle(
            brush: PdfSolidBrush(pdfColor),
            bounds: Rect.fromLTWH(
              0,
              patientTableY + patientGridMargin,
              pageSize.width,
              patientDataHeight,
            ),
          );

          page.graphics.restore();
        } else if (background['type'] == 'image') {
          final Uint8List imageData = background['data'] as Uint8List;
          final double alpha = background['alpha'] as double? ?? 1.0;
          final PdfBitmap bgImage = PdfBitmap(imageData);

          page.graphics.save();
          page.graphics.setTransparency(alpha);

          page.graphics.drawImage(
            bgImage,
            Rect.fromLTWH(
              0,
              patientTableY + patientGridMargin,
              pageSize.width,
              patientDataHeight,
            ),
          );

          page.graphics.restore();
        }
      } catch (e) {
        debugPrint('Error drawing patient-data background: $e');
      }
    }

    final PdfGrid patientGrid = PdfGrid();
    patientGrid.columns.add(count: 4);

    patientGrid.columns[0].width = pageSize.width * 0.15;
    patientGrid.columns[1].width = pageSize.width * 0.35;
    patientGrid.columns[2].width = pageSize.width * 0.15;
    patientGrid.columns[3].width = pageSize.width * 0.35;

    patientGrid.headers.add(1);
    final PdfGridRow patientHeaderRow = patientGrid.headers[0];
    patientHeaderRow.height = 16;
    patientHeaderRow.cells[0].value = 'Patient Information';
    patientHeaderRow.cells[0].columnSpan = 4;
    patientHeaderRow.cells[0].style.font = PdfTrueTypeFont(
      notoFontData,
      pdfFontSize(FontSizes.bodySmall),
    );

    patientHeaderRow.cells[0].style.textBrush = PdfSolidBrush(
      hexToPdfColor(userProfile.patientInfoTextColor),
    );
    patientHeaderRow.cells[0].style.stringFormat = PdfStringFormat(
      alignment: PdfTextAlignment.center,
      lineAlignment: PdfVerticalAlignment.middle,
    );
    patientHeaderRow.cells[0].style.cellPadding = PdfPaddings(
      left: 2,
      right: 2,
      top: 1,
      bottom: 1,
    );

    final PdfGridRow row1 = patientGrid.rows.add();
    row1.cells[0].value = 'Patient Name:';
    row1.cells[1].value =
        patientData != null &&
                patientData['patientInfo'] != null &&
                patientData['patientInfo']['name'] != null
            ? patientData['patientInfo']['name']
            : 'Not provided';
    row1.cells[2].value = 'Exam Date/Time:';
    final now = DateTime.now();
    final hour = now.hour.toString().padLeft(2, '0');
    final minute = now.minute.toString().padLeft(2, '0');
    row1.cells[3].value = '${now.day}/${now.month}/${now.year} $hour:$minute';

    final PdfGridRow row2 = patientGrid.rows.add();
    row2.cells[0].value = 'Gender:';
    row2.cells[1].value =
        patientData != null &&
                patientData['patientInfo'] != null &&
                patientData['patientInfo']['gender'] != null
            ? patientData['patientInfo']['gender']
            : 'Not provided';
    row2.cells[2].value = 'Age:';
    row2.cells[3].value =
        patientData != null &&
                patientData['patientInfo'] != null &&
                patientData['patientInfo']['age'] != null
            ? '${patientData['patientInfo']['age']} years'
            : 'Not provided';

    final PdfGridRow row3 = patientGrid.rows.add();
    row3.cells[0].value = 'Priority:';
    row3.cells[1].value =
        patientData != null &&
                patientData['patientInfo'] != null &&
                patientData['patientInfo']['priority'] != null
            ? patientData['patientInfo']['priority']
            : 'Routine';
    row3.cells[2].value = 'Location:';
    row3.cells[3].value =
        patientData != null &&
                patientData['patientInfo'] != null &&
                patientData['patientInfo']['location'] != null
            ? patientData['patientInfo']['location'] == 'other'
                ? 'Clinic'
                : patientData['patientInfo']['location']
            : 'Outpatient';

    final PdfGridRow row4 = patientGrid.rows.add();
    row4.cells[0].value = 'Indication:';
    row4.cells[1].value =
        patientData != null &&
                patientData['patientInfo'] != null &&
                patientData['patientInfo']['indication'] != null
            ? patientData['patientInfo']['indication']
            : 'Not specified';
    row4.cells[2].value = 'Requested By:';
    row4.cells[3].value =
        patientData != null &&
                patientData['patientInfo'] != null &&
                patientData['patientInfo']['requestedBy'] != null
            ? patientData['patientInfo']['requestedBy']
            : 'N/A';

    for (int i = 0; i < patientGrid.rows.count; i++) {
      final row = patientGrid.rows[i];

      row.height = 18;

      for (int j = 0; j < row.cells.count; j++) {
        final cell = row.cells[j];

        cell.style.font = PdfTrueTypeFont(
          notoFontData,
          pdfFontSize(FontSizes.bodySmall) - 1,
        );

        if (j % 2 == 0) {
          cell.style.font = PdfTrueTypeFont(
            notoFontData,
            pdfFontSize(FontSizes.bodySmall) - 1,
          );
          cell.style.stringFormat = PdfStringFormat(
            alignment: PdfTextAlignment.left,
            lineAlignment: PdfVerticalAlignment.middle,
          );
        } else {
          cell.style.stringFormat = PdfStringFormat(
            alignment: PdfTextAlignment.left,
            lineAlignment: PdfVerticalAlignment.middle,
          );
        }

        cell.style.borders.all = PdfPen(PdfColor(180, 180, 180), width: 0.5);

        if (i == 0) {
          cell.style.borders.top = PdfPen(PdfColor(0, 0, 0), width: 1.0);
        }
        if (i == patientGrid.rows.count - 1) {
          cell.style.borders.bottom = PdfPen(PdfColor(0, 0, 0), width: 1.0);
        }
        if (j == 0) {
          cell.style.borders.left = PdfPen(PdfColor(0, 0, 0), width: 1.0);
        }
        if (j == 1) {
          cell.style.borders.right = PdfPen(PdfColor(0, 0, 0), width: 1.0);
        }
        if (j == 3) {
          cell.style.borders.right = PdfPen(PdfColor(0, 0, 0), width: 1.0);
        }

        cell.style.cellPadding = PdfPaddings(
          left: 2,
          right: 2,
          top: 1,
          bottom: 1,
        );
      }
    }

    final PdfLayoutResult? patientGridResult = patientGrid.draw(
      page: page,
      bounds: Rect.fromLTWH(
        0,
        patientTableY + patientGridMargin,
        pageSize.width,
        0,
      ),
      format: PdfLayoutFormat(layoutType: PdfLayoutType.paginate),
    );

    final double echoTableY =
        (patientGridResult?.bounds.bottom ?? patientTableY) + 3;

    final double echoParametersHeight = 16 + (4 * 18);

    if (positionedBackgrounds.containsKey('echo-parameters')) {
      try {
        final dynamic background = positionedBackgrounds['echo-parameters'];

        if (background['type'] == 'color') {
          final String colorHex = background['color'] as String;
          final double alpha = background['alpha'] as double;

          final PdfColor pdfColor = hexToPdfColor(colorHex);

          page.graphics.save();

          page.graphics.setTransparency(alpha);

          page.graphics.drawRectangle(
            brush: PdfSolidBrush(pdfColor),
            bounds: Rect.fromLTWH(
              0,
              echoTableY,
              pageSize.width,
              echoParametersHeight,
            ),
          );

          page.graphics.restore();
        } else if (background['type'] == 'image') {
          final Uint8List imageData = background['data'] as Uint8List;
          final double alpha = background['alpha'] as double? ?? 1.0;
          final PdfBitmap bgImage = PdfBitmap(imageData);

          page.graphics.save();
          page.graphics.setTransparency(alpha);

          page.graphics.drawImage(
            bgImage,
            Rect.fromLTWH(0, echoTableY, pageSize.width, echoParametersHeight),
          );

          page.graphics.restore();
        }
      } catch (e) {
        debugPrint('Error drawing echo-parameters background: $e');
      }
    }

    final PdfGrid echoGrid = PdfGrid();
    echoGrid.columns.add(count: 6);

    echoGrid.columns[0].width = pageSize.width * 0.15;
    echoGrid.columns[1].width = pageSize.width * 0.15;
    echoGrid.columns[2].width = pageSize.width * 0.20;
    echoGrid.columns[3].width = pageSize.width * 0.15;
    echoGrid.columns[4].width = pageSize.width * 0.15;
    echoGrid.columns[5].width = pageSize.width * 0.20;

    echoGrid.headers.add(1);
    final PdfGridRow echoHeaderRow = echoGrid.headers[0];
    echoHeaderRow.height = 16;
    echoHeaderRow.cells[0].value = 'Echo Parameters';
    echoHeaderRow.cells[0].columnSpan = 6;
    echoHeaderRow.cells[0].style.font = PdfTrueTypeFont(
      notoFontData,
      pdfFontSize(FontSizes.bodySmall),
    );

    echoHeaderRow.cells[0].style.textBrush = PdfSolidBrush(
      hexToPdfColor(userProfile.echoParametersTextColor),
    );
    echoHeaderRow.cells[0].style.stringFormat = PdfStringFormat(
      alignment: PdfTextAlignment.center,
      lineAlignment: PdfVerticalAlignment.middle,
    );
    echoHeaderRow.cells[0].style.cellPadding = PdfPaddings(
      left: 2,
      right: 2,
      top: 1,
      bottom: 1,
    );

    final String? patientGender =
        patientData != null &&
                patientData['patientInfo'] != null &&
                patientData['patientInfo']['gender'] != null
            ? patientData['patientInfo']['gender']
            : null;

    final Map<String, String> normalRanges = _getGenderSpecificNormalRanges(
      patientGender,
    );

    final PdfGridRow echoRow1 = echoGrid.rows.add();
    echoRow1.cells[0].value = 'LVEDD (mm):';
    echoRow1.cells[1].value =
        patientData != null &&
                patientData['echoParameters'] != null &&
                patientData['echoParameters']['lvedd'] != null
            ? '${patientData['echoParameters']['lvedd']}'
            : 'Not measured';
    echoRow1.cells[2].value = 'Normal Range: ${normalRanges['lvedd']}';

    echoRow1.cells[3].value = 'LVESD (mm):';
    echoRow1.cells[4].value =
        patientData != null &&
                patientData['echoParameters'] != null &&
                patientData['echoParameters']['lvesd'] != null
            ? '${patientData['echoParameters']['lvesd']}'
            : 'Not measured';
    echoRow1.cells[5].value = 'Normal Range: ${normalRanges['lvesd']}';

    final PdfGridRow echoRow2 = echoGrid.rows.add();
    echoRow2.cells[0].value = 'IVSD (mm):';
    echoRow2.cells[1].value =
        patientData != null &&
                patientData['echoParameters'] != null &&
                patientData['echoParameters']['ivsd'] != null
            ? '${patientData['echoParameters']['ivsd']}'
            : 'Not measured';
    echoRow2.cells[2].value = 'Normal Range: ${normalRanges['ivsd']}';

    echoRow2.cells[3].value = 'LVPW (mm):';
    echoRow2.cells[4].value =
        patientData != null &&
                patientData['echoParameters'] != null &&
                patientData['echoParameters']['lvpw'] != null
            ? '${patientData['echoParameters']['lvpw']}'
            : 'Not measured';
    echoRow2.cells[5].value = 'Normal Range: ${normalRanges['lvpw']}';

    final PdfGridRow echoRow3 = echoGrid.rows.add();
    echoRow3.cells[0].value = 'LA (mm):';
    echoRow3.cells[1].value =
        patientData != null &&
                patientData['echoParameters'] != null &&
                patientData['echoParameters']['la'] != null
            ? '${patientData['echoParameters']['la']}'
            : 'Not measured';
    echoRow3.cells[2].value = 'Normal Range: ${normalRanges['la']}';

    echoRow3.cells[3].value = 'AO (mm):';
    echoRow3.cells[4].value =
        patientData != null &&
                patientData['echoParameters'] != null &&
                patientData['echoParameters']['ao'] != null
            ? '${patientData['echoParameters']['ao']}'
            : 'Not measured';
    echoRow3.cells[5].value = 'Normal Range: ${normalRanges['ao']}';

    final PdfGridRow echoRow4 = echoGrid.rows.add();
    echoRow4.cells[0].value = 'EF (%):';
    echoRow4.cells[1].value =
        patientData != null &&
                patientData['echoParameters'] != null &&
                patientData['echoParameters']['ef'] != null
            ? '${patientData['echoParameters']['ef']}'
            : 'Not measured';
    echoRow4.cells[2].value = 'Normal Range: ${normalRanges['ef']}';

    echoRow4.cells[3].value = 'FS (%):';
    echoRow4.cells[4].value =
        patientData != null &&
                patientData['echoParameters'] != null &&
                patientData['echoParameters']['fs'] != null
            ? '${patientData['echoParameters']['fs']}'
            : 'Not measured';
    echoRow4.cells[5].value = 'Normal Range: ${normalRanges['fs']}';

    for (int i = 0; i < echoGrid.rows.count; i++) {
      final row = echoGrid.rows[i];

      row.height = 18;

      for (int j = 0; j < row.cells.count; j++) {
        final cell = row.cells[j];

        cell.style.font = PdfTrueTypeFont(
          notoFontData,
          pdfFontSize(FontSizes.bodySmall) - 1,
        );

        if (j % 2 == 0) {
          cell.style.font = PdfTrueTypeFont(
            notoFontData,
            pdfFontSize(FontSizes.bodySmall) - 1,
          );
          cell.style.stringFormat = PdfStringFormat(
            alignment: PdfTextAlignment.left,
            lineAlignment: PdfVerticalAlignment.middle,
          );
        } else {
          cell.style.stringFormat = PdfStringFormat(
            alignment: PdfTextAlignment.left,
            lineAlignment: PdfVerticalAlignment.middle,
          );
        }

        cell.style.borders.all = PdfPen(PdfColor(180, 180, 180), width: 0.5);

        if (i == 0) {
          cell.style.borders.top = PdfPen(PdfColor(0, 0, 0), width: 1.0);
        }
        if (i == echoGrid.rows.count - 1) {
          cell.style.borders.bottom = PdfPen(PdfColor(0, 0, 0), width: 1.0);
        }
        if (j == 0) {
          cell.style.borders.left = PdfPen(PdfColor(0, 0, 0), width: 1.0);
        }
        if (j == 2) {
          cell.style.borders.right = PdfPen(PdfColor(0, 0, 0), width: 1.0);
        }
        if (j == 5) {
          cell.style.borders.right = PdfPen(PdfColor(0, 0, 0), width: 1.0);
        }

        cell.style.cellPadding = PdfPaddings(
          left: 2,
          right: 2,
          top: 1,
          bottom: 1,
        );
      }
    }

    final PdfLayoutResult? echoGridResult = echoGrid.draw(
      page: page,
      bounds: Rect.fromLTWH(0, echoTableY, pageSize.width, 0),
    );

    final double nextSectionY =
        (echoGridResult?.bounds.bottom ?? echoTableY) + 2;

    const double headerHeight = 115;
    const double footerHeight = 70;
    const double interpretationMinHeight = 300;

    PdfPage currentPage = page;
    double currentY = nextSectionY;

    if (currentY + interpretationMinHeight > pageSize.height - footerHeight) {
      currentPage = document.pages.add();
      currentY = headerHeight;
    }

    final double interpretationY = currentY;

    if (positionedBackgrounds.containsKey('interpretation')) {
      try {
        double footerY = pageSize.height - 60;
        final dynamic background = positionedBackgrounds['interpretation'];

        if (background['type'] == 'color') {
          final String colorHex = background['color'] as String;
          final double alpha = background['alpha'] as double;

          final PdfColor pdfColor = hexToPdfColor(colorHex);

          page.graphics.save();

          page.graphics.setTransparency(alpha);

          page.graphics.drawRectangle(
            brush: PdfSolidBrush(pdfColor),
            bounds: Rect.fromLTWH(
              0,
              interpretationY,
              pageSize.width,
              footerY - interpretationY,
            ),
          );

          page.graphics.restore();
        } else if (background['type'] == 'image') {
          final Uint8List imageData = background['data'] as Uint8List;
          final double alpha = background['alpha'] as double? ?? 1.0;
          final PdfBitmap bgImage = PdfBitmap(imageData);

          page.graphics.save();
          page.graphics.setTransparency(alpha);

          page.graphics.drawImage(
            bgImage,
            Rect.fromLTWH(
              0,
              interpretationY,
              pageSize.width,
              footerY - interpretationY,
            ),
          );

          page.graphics.restore();
        }
      } catch (e) {
        debugPrint('Error drawing interpretation background: $e');
      }
    }

    final PdfFont sectionTitleFont = PdfTrueTypeFont(
      notoFontData,
      pdfFontSize(FontSizes.heading3),
    );

    currentPage.graphics.drawString(
      'Interpretation Details',
      sectionTitleFont,
      brush: PdfSolidBrush(hexToPdfColor(userProfile.interpretationTextColor)),
      format: PdfStringFormat(alignment: PdfTextAlignment.center),
      bounds: Rect.fromLTWH(0, interpretationY, pageSize.width, 25),
    );

    final double contentY = interpretationY + 30;

    final double totalInterpretationWidth = pageSize.width - 40;
    final double textAreaWidth = totalInterpretationWidth * 0.85;
    final double imageAreaWidth = totalInterpretationWidth * 0.15;
    final double imageAreaX = 20 + textAreaWidth + 10;

    final double imageSize = 80;
    final double chartSize = 80;

    final double imagesStartY = contentY + 1;
    final double chartY = imagesStartY;
    final double chartX = imageAreaX + (imageAreaWidth - chartSize);

    final double hlaY = chartY + chartSize + 40;
    final double hlaX = imageAreaX + (imageAreaWidth - imageSize);

    _drawHeartSegmentChart(
      currentPage,
      wallMotionScores,
      chartX,
      chartY,
      chartSize,
    );

    currentPage.graphics.drawString(
      'Horizontal long Axis',
      PdfStandardFont(PdfFontFamily.helvetica, 8, style: PdfFontStyle.bold),
      brush: PdfSolidBrush(PdfColor(0, 0, 0)),
      format: PdfStringFormat(alignment: PdfTextAlignment.center),
      bounds: Rect.fromLTWH(hlaX, hlaY - 12, imageSize, 12),
    );

    _drawHLAImageWithSegments(
      currentPage,
      wallMotionScores,
      hlaX,
      hlaY,
      imageSize,
      imageSize,
      hlaImageData,
      notoFontData,
    );

    final double vlaY = hlaY + imageSize + 40;
    final double vlaX = hlaX;

    currentPage.graphics.drawString(
      'Vertical long Axis',
      PdfStandardFont(PdfFontFamily.helvetica, 8, style: PdfFontStyle.bold),
      brush: PdfSolidBrush(PdfColor(0, 0, 0)),
      format: PdfStringFormat(alignment: PdfTextAlignment.center),
      bounds: Rect.fromLTWH(vlaX, vlaY - 12, imageSize, 12),
    );

    _drawVLAImageWithSegments(
      currentPage,
      wallMotionScores,
      vlaX,
      vlaY,
      imageSize,
      60,
      data.vlaImageData,
      notoFontData,
    );

    final String affectedArteries = _getAffectedCoronaryArteries(
      wallMotionScores,
    );
    if (affectedArteries.isNotEmpty) {
      final double arteriesY = vlaY + 60 + 5;
      currentPage.graphics.drawString(
        affectedArteries,
        PdfStandardFont(PdfFontFamily.helvetica, 7),
        brush: PdfSolidBrush(PdfColor(0, 0, 0)),
        format: PdfStringFormat(alignment: PdfTextAlignment.center),
        bounds: Rect.fromLTWH(vlaX, arteriesY, imageSize, 20),
      );
    }

    final double footerPosition = pageSize.height - 60;
    final double legendX = imageAreaX + (imageAreaWidth - 60);
    final double legendY = footerPosition - (5 * (12 + 3)) - 5;
    final double legendItemHeight = 12;
    final double legendItemWidth = 12;
    final double legendTextX = legendX + legendItemWidth + 3;

    currentPage.graphics.drawRectangle(
      brush: PdfSolidBrush(PdfColor(0, 204, 0)),
      bounds: Rect.fromLTWH(
        legendX,
        legendY,
        legendItemWidth,
        legendItemHeight,
      ),
    );
    currentPage.graphics.drawString(
      'Normal',
      PdfTrueTypeFont(notoFontData, 7),
      brush: PdfSolidBrush(PdfColor(0, 0, 0)),
      bounds: Rect.fromLTWH(legendTextX, legendY, 50, legendItemHeight),
    );

    currentPage.graphics.drawRectangle(
      brush: PdfSolidBrush(PdfColor(255, 204, 0)),
      bounds: Rect.fromLTWH(
        legendX,
        legendY + legendItemHeight + 3,
        legendItemWidth,
        legendItemHeight,
      ),
    );
    currentPage.graphics.drawString(
      'Hypokinetic',
      PdfTrueTypeFont(notoFontData, 7),
      brush: PdfSolidBrush(PdfColor(0, 0, 0)),
      bounds: Rect.fromLTWH(
        legendTextX,
        legendY + legendItemHeight + 3,
        50,
        legendItemHeight,
      ),
    );

    currentPage.graphics.drawRectangle(
      brush: PdfSolidBrush(PdfColor(255, 102, 0)),
      bounds: Rect.fromLTWH(
        legendX,
        legendY + (legendItemHeight + 3) * 2,
        legendItemWidth,
        legendItemHeight,
      ),
    );
    currentPage.graphics.drawString(
      'Akinetic',
      PdfTrueTypeFont(notoFontData, 7),
      brush: PdfSolidBrush(PdfColor(0, 0, 0)),
      bounds: Rect.fromLTWH(
        legendTextX,
        legendY + (legendItemHeight + 3) * 2,
        50,
        legendItemHeight,
      ),
    );

    currentPage.graphics.drawRectangle(
      brush: PdfSolidBrush(PdfColor(165, 42, 42)),
      bounds: Rect.fromLTWH(
        legendX,
        legendY + (legendItemHeight + 3) * 3,
        legendItemWidth,
        legendItemHeight,
      ),
    );
    currentPage.graphics.drawString(
      'Dyskinetic',
      PdfTrueTypeFont(notoFontData, 7),
      brush: PdfSolidBrush(PdfColor(0, 0, 0)),
      bounds: Rect.fromLTWH(
        legendTextX,
        legendY + (legendItemHeight + 3) * 3,
        50,
        legendItemHeight,
      ),
    );

    currentPage.graphics.drawRectangle(
      brush: PdfSolidBrush(PdfColor(204, 0, 255)),
      bounds: Rect.fromLTWH(
        legendX,
        legendY + (legendItemHeight + 3) * 4,
        legendItemWidth,
        legendItemHeight,
      ),
    );
    currentPage.graphics.drawString(
      'Aneurysmal',
      PdfTrueTypeFont(notoFontData, 7),
      brush: PdfSolidBrush(PdfColor(0, 0, 0)),
      bounds: Rect.fromLTWH(
        legendTextX,
        legendY + (legendItemHeight + 3) * 4,
        50,
        legendItemHeight,
      ),
    );

    final double interpretationWidth = textAreaWidth;

    final PdfFont interpretationTextFont = PdfTrueTypeFont(
      notoFontData,
      pdfFontSize(FontSizes.bodySmall),
    );

    final double availableTextHeight = footerPosition - contentY - 10;

    // Debug: Print space calculations
    debugPrint('PDF Space Debug - Page height: ${pageSize.height}');
    debugPrint('PDF Space Debug - contentY: $contentY');
    debugPrint('PDF Space Debug - footerPosition: $footerPosition');
    debugPrint('PDF Space Debug - availableTextHeight: $availableTextHeight');

    final StringBuffer buffer = StringBuffer();
    for (final section in interpretationSections) {
      buffer.writeln(section.title);

      // Handle bullet points or regular content
      if (section.isBulletFormat &&
          section.bulletPoints != null &&
          section.bulletPoints!.isNotEmpty) {
        // Debug: Print bullet points count
        debugPrint(
          'PDF Generation - Section: ${section.title}, Bullet points count: ${section.bulletPoints!.length}',
        );
        for (int i = 0; i < section.bulletPoints!.length; i++) {
          debugPrint('PDF Generation - Bullet $i: ${section.bulletPoints![i]}');
        }

        // Add bullet points with simple dash instead of bullet character
        for (final point in section.bulletPoints!) {
          buffer.writeln('- $point');
        }
      } else {
        // Add regular content
        buffer.writeln(section.content);
      }

      buffer.writeln(); // Add spacing between sections
    }

    String interpretationText = buffer.toString();
    if (interpretationText.trim().isEmpty) {
      interpretationText = 'Normal study.';
    }

    final PdfTextElement textElement = PdfTextElement(
      text: interpretationText,
      font: interpretationTextFont,
      brush: PdfSolidBrush(hexToPdfColor(userProfile.interpretationTextColor)),
      format: PdfStringFormat(
        alignment: PdfTextAlignment.left,
        lineSpacing: 2, // Increase line spacing for better readability
      ),
    );

    textElement.draw(
      page: currentPage,
      bounds: Rect.fromLTWH(
        10,
        contentY,
        interpretationWidth,
        availableTextHeight,
      ),
      format: PdfLayoutFormat(
        layoutType: PdfLayoutType.paginate,
        breakType: PdfLayoutBreakType.fitPage, // Better page fitting
      ),
    );

    for (int i = 0; i < document.pages.count; i++) {
      final PdfPage currentPage = document.pages[i];
      _drawHeaderAndFooter(
        currentPage,
        userProfile,
        notoFontData,
        logoImageData,
        backgroundImageData,
        positionedBackgrounds,
        i + 1,
        document.pages.count,
      );
    }

    final List<int> bytes = document.saveSync();
    final Uint8List pdfBytes = Uint8List.fromList(bytes);

    document.dispose();

    return pdfBytes;
  }

  static void _drawHeaderAndFooter(
    PdfPage page,
    UserProfile userProfile,
    Uint8List notoFontData,
    Uint8List logoImageData,
    Uint8List? backgroundImageData,
    Map<String, dynamic> positionedBackgrounds,
    int pageNumber,
    int pageCount,
  ) {
    final Size pageSize = page.getClientSize();

    // Draw Background
    if (backgroundImageData != null && backgroundImageData.isNotEmpty) {
      try {
        final PdfBitmap background = PdfBitmap(backgroundImageData);
        final double squareSize = math.min(pageSize.width, pageSize.height);
        final double xOffset = (pageSize.width - squareSize) / 2;
        final double yOffset = (pageSize.height - squareSize) / 2;
        page.graphics.save();
        page.graphics.setTransparency(userProfile.pdfBackgroundTransparency);
        page.graphics.drawImage(
          background,
          Rect.fromLTWH(xOffset, yOffset, squareSize, squareSize),
        );
        page.graphics.restore();
      } catch (e) {
        // Ignore
      }
    }

    if (positionedBackgrounds.isNotEmpty) {
      try {
        final double halfWidth = pageSize.width / 2;
        final double halfHeight = pageSize.height / 2;
        positionedBackgrounds.forEach((position, background) {
          if (position == 'center' ||
              [
                'header',
                'patient-data',
                'echo-parameters',
                'interpretation',
                'footer',
              ].contains(position)) {
            return;
          }
          double x = 0, y = 0, width = halfWidth, height = halfHeight;
          switch (position) {
            case 'top-left':
              x = 0;
              y = 0;
              break;
            case 'top-right':
              x = halfWidth;
              y = 0;
              break;
            case 'bottom-left':
              x = 0;
              y = halfHeight;
              break;
            case 'bottom-right':
              x = halfWidth;
              y = halfHeight;
              break;
            default:
              return;
          }
          try {
            if (background['type'] == 'color') {
              final PdfColor pdfColor = hexToPdfColor(background['color']);
              page.graphics.save();
              page.graphics.setTransparency(background['alpha']);
              page.graphics.drawRectangle(
                brush: PdfSolidBrush(pdfColor),
                bounds: Rect.fromLTWH(x, y, width, height),
              );
              page.graphics.restore();
            } else if (background['type'] == 'image') {
              final PdfBitmap bgImage = PdfBitmap(background['data']);
              page.graphics.save();
              page.graphics.setTransparency(background['alpha'] ?? 1.0);
              page.graphics.drawImage(
                bgImage,
                Rect.fromLTWH(x, y, width, height),
              );
              page.graphics.restore();
            }
          } catch (e) {
            // Ignore
          }
        });
      } catch (e) {
        // Ignore
      }
    }

    // Draw Header
    if (pageNumber == 1) {
      final PdfGrid headerGrid = PdfGrid();
      headerGrid.columns.add(count: 3);
      for (int i = 0; i < headerGrid.columns.count; i++) {
        headerGrid.columns[i].width = pageSize.width / 3;
      }
      final PdfGridRow headerRow = headerGrid.rows.add();
      headerRow.height = 90;
      final PdfFont notoFont = PdfTrueTypeFont(
        notoFontData,
        pdfFontSize(FontSizes.bodyLarge),
      );

      String leftHeaderText = '';
      if (userProfile.useArabicLeftHeader) {
        if (userProfile.doctorNameAr.isNotEmpty) {
          leftHeaderText += userProfile.doctorNameAr;
        }
        if (userProfile.doctorDegreeAr.isNotEmpty) {
          leftHeaderText += '\n${userProfile.doctorDegreeAr}';
        }
        if (userProfile.doctorLocationAr.isNotEmpty) {
          leftHeaderText += '\n${userProfile.doctorLocationAr}';
        }
        if (userProfile.doctorSpecializationAr.isNotEmpty) {
          leftHeaderText += '\n${userProfile.doctorSpecializationAr}';
        }
      } else {
        if (userProfile.doctorNameEn.isNotEmpty) {
          leftHeaderText += userProfile.doctorNameEn;
        }
        if (userProfile.doctorDegreeEn.isNotEmpty) {
          leftHeaderText += '\n${userProfile.doctorDegreeEn}';
        }
        if (userProfile.doctorLocationEn.isNotEmpty) {
          leftHeaderText += '\n${userProfile.doctorLocationEn}';
        }
        if (userProfile.doctorSpecializationEn.isNotEmpty) {
          leftHeaderText += '\n${userProfile.doctorSpecializationEn}';
        }
      }
      headerRow.cells[0].value =
          leftHeaderText.isEmpty ? 'Dr. Waleed Sheha\nICU' : leftHeaderText;

      headerRow.cells[0].style.font = notoFont;
      headerRow.cells[0].style.textBrush = PdfSolidBrush(
        hexToPdfColor(userProfile.doctorTitleColor),
      );

      // Set RTL text direction for left header if it contains Arabic text
      if (_containsArabic(headerRow.cells[0].value.toString())) {
        headerRow.cells[0].style.stringFormat = PdfStringFormat(
          alignment: PdfTextAlignment.center,
          textDirection: PdfTextDirection.rightToLeft,
          lineAlignment: PdfVerticalAlignment.top,
        );
      }

      String rightHeaderText = '';
      if (userProfile.useArabicHeader) {
        if (userProfile.rightDoctorNameAr.isNotEmpty) {
          rightHeaderText += userProfile.rightDoctorNameAr;
        }
        if (userProfile.rightDoctorDegreeAr.isNotEmpty) {
          rightHeaderText += '\n${userProfile.rightDoctorDegreeAr}';
        }
        if (userProfile.rightDoctorLocationAr.isNotEmpty) {
          rightHeaderText += '\n${userProfile.rightDoctorLocationAr}';
        }
        if (userProfile.rightDoctorSpecializationAr.isNotEmpty) {
          rightHeaderText += '\n${userProfile.rightDoctorSpecializationAr}';
        }
      } else {
        rightHeaderText = userProfile.headerText;
      }
      final String arabicText =
          rightHeaderText.isEmpty
              ? 'وليد شيحه\nطبيب الرعايه المركزه'
              : rightHeaderText;
      headerRow.cells[2].value = arabicText;

      if (_containsArabic(arabicText)) {
        headerRow.cells[2].style.font = notoFont;
        headerRow.cells[2].style.textBrush = PdfSolidBrush(
          hexToPdfColor(userProfile.doctorTitleColor),
        );
        headerRow.cells[2].style.stringFormat = PdfStringFormat(
          alignment: PdfTextAlignment.center,
          textDirection: PdfTextDirection.rightToLeft,
          lineAlignment: PdfVerticalAlignment.top,
        );
      } else {
        headerRow.cells[2].style.textBrush = PdfSolidBrush(
          hexToPdfColor(userProfile.doctorTitleColor),
        );
      }

      for (int i = 0; i < headerRow.cells.count; i++) {
        final cell = headerRow.cells[i];
        cell.style.borders.all = PdfPen(PdfColor(255, 255, 255, 0));
        cell.style.cellPadding = PdfPaddings(
          left: 2,
          right: 2,
          top: 2,
          bottom: 2,
        );
        if ((i == 0 && !_containsArabic(cell.value.toString())) ||
            i == 1 ||
            (i == 2 && !_containsArabic(cell.value.toString()))) {
          cell.style.stringFormat = PdfStringFormat(
            alignment: PdfTextAlignment.center,
            lineAlignment: PdfVerticalAlignment.top,
          );
        }
      }

      if (positionedBackgrounds.containsKey('header')) {
        try {
          final dynamic background = positionedBackgrounds['header'];
          final double headerImageHeight = 90;
          if (background['type'] == 'color') {
            final PdfColor pdfColor = hexToPdfColor(background['color']);
            page.graphics.save();
            page.graphics.setTransparency(background['alpha']);
            page.graphics.drawRectangle(
              brush: PdfSolidBrush(pdfColor),
              bounds: Rect.fromLTWH(0, 0, pageSize.width, headerImageHeight),
            );
            page.graphics.restore();
          } else if (background['type'] == 'image') {
            final PdfBitmap bgImage = PdfBitmap(background['data']);
            page.graphics.save();
            page.graphics.setTransparency(background['alpha'] ?? 1.0);
            page.graphics.drawImage(
              bgImage,
              Rect.fromLTWH(0, 0, pageSize.width, headerImageHeight),
            );
            page.graphics.restore();
          }
        } catch (e) {
          // Ignore
        }
      }

      final PdfLayoutResult? layoutResult = headerGrid.draw(
        page: page,
        bounds: Rect.fromLTWH(0, 0, pageSize.width, 0),
      );
      final double gridTop = layoutResult?.bounds.top ?? 0;

      if (logoImageData.length > 4) {
        final PdfBitmap logo = PdfBitmap(logoImageData);
        final double logoMaxHeight = 80;
        final double aspectRatio = logo.width / logo.height;
        final double logoHeight = logoMaxHeight;
        final double logoWidth = logoHeight * aspectRatio;
        final double logoX = (pageSize.width / 2) - (logoWidth / 2);
        final double logoY =
            (gridTop + (headerRow.height / 2)) - (logoHeight / 2) - 3;
        page.graphics.drawImage(
          logo,
          Rect.fromLTWH(logoX, logoY, logoWidth, logoHeight),
        );
      }

      final PdfFont titleFont = PdfTrueTypeFont(
        notoFontData,
        pdfFontSize(FontSizes.heading3),
      );
      final double titleY = gridTop + headerRow.height + 5;
      page.graphics.drawString(
        'Echocardiogram Report',
        titleFont,
        brush: PdfSolidBrush(hexToPdfColor(userProfile.reportTitleColor)),
        format: PdfStringFormat(alignment: PdfTextAlignment.center),
        bounds: Rect.fromLTWH(0, titleY, pageSize.width, 25),
      );
    }
    // Draw Footer
    double footerY = pageSize.height - 60;
    if (positionedBackgrounds.containsKey('footer')) {
      try {
        final dynamic background = positionedBackgrounds['footer'];
        if (background['type'] == 'color') {
          final PdfColor pdfColor = hexToPdfColor(background['color']);
          page.graphics.save();
          page.graphics.setTransparency(background['alpha']);
          page.graphics.drawRectangle(
            brush: PdfSolidBrush(pdfColor),
            bounds: Rect.fromLTWH(
              0,
              footerY,
              pageSize.width,
              pageSize.height - footerY,
            ),
          );
          page.graphics.restore();
        } else if (background['type'] == 'image') {
          final PdfBitmap bgImage = PdfBitmap(background['data']);
          page.graphics.save();
          page.graphics.setTransparency(background['alpha'] ?? 1.0);
          page.graphics.drawImage(
            bgImage,
            Rect.fromLTWH(
              0,
              footerY,
              pageSize.width,
              pageSize.height - footerY,
            ),
          );
          page.graphics.restore();
        }
      } catch (e) {
        // Ignore
      }
    }

    page.graphics.drawLine(
      PdfPen(PdfColor(0, 0, 0), width: 1.0),
      Offset(0, footerY),
      Offset(pageSize.width, footerY),
    );

    final PdfFont footerFont = PdfTrueTypeFont(
      notoFontData,
      pdfFontSize(FontSizes.bodySmall),
    );
    page.graphics.drawString(
      userProfile.doctorNameEn.isNotEmpty
          ? userProfile.doctorNameEn.toUpperCase()
          : 'DR. WALEED SHEHA',
      footerFont,
      brush: PdfSolidBrush(hexToPdfColor(userProfile.footerTextColor)),
      bounds: Rect.fromLTWH(20, footerY + 5, 300, 20),
    );

    String credentialsText = '';
    if (userProfile.doctorDegreeEn.isNotEmpty) {
      credentialsText = userProfile.doctorDegreeEn;
      if (userProfile.doctorLocationEn.isNotEmpty) {
        credentialsText += ' - ${userProfile.doctorLocationEn}';
      }
      if (userProfile.doctorSpecializationEn.isNotEmpty) {
        credentialsText += ' - ${userProfile.doctorSpecializationEn}';
      }
    } else {
      credentialsText = 'MD, FACC - Consultant Cardiologist';
    }
    page.graphics.drawString(
      credentialsText,
      footerFont,
      brush: PdfSolidBrush(hexToPdfColor(userProfile.footerTextColor)),
      bounds: Rect.fromLTWH(20, footerY + 25, 300, 20),
    );

    page.graphics.drawString(
      'Page $pageNumber of $pageCount',
      footerFont,
      brush: PdfSolidBrush(hexToPdfColor(userProfile.footerTextColor)),
      format: PdfStringFormat(alignment: PdfTextAlignment.right),
      bounds: Rect.fromLTWH(pageSize.width - 250, footerY + 20, 230, 20),
    );
  }

  static bool _containsArabic(String text) {
    final RegExp arabicRegex = RegExp(r'[\u0600-\u06FF]');
    return arabicRegex.hasMatch(text);
  }

  static Future<Uint8List> _getLogoImage(UserProfile userProfile) async {
    try {
      if (userProfile.logoImagePath != null) {
        final logoFile = await UserProfileService.getLogoImageFile();
        if (logoFile != null) {
          return logoFile.readAsBytesSync();
        }
      }

      if (userProfile.toJson().containsKey('logoImagePath') &&
          userProfile.logoImagePath == null) {
        return Uint8List.fromList([0, 0, 0, 0]);
      }

      final ByteData data = await rootBundle.load(
        'lib/assets/images/syvursoft.jpeg',
      );
      return data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
    } catch (e) {
      final ByteData data = await rootBundle.load(
        'lib/assets/images/syvursoft.jpeg',
      );
      return data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
    }
  }

  static Future<Uint8List> _getArabicFont() async {
    final ByteData data = await rootBundle.load(
      'lib/assets/fonts/NotoNaskhArabic-Medium.ttf',
    );
    return data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
  }

  static Future<Uint8List> _getHLAImage() async {
    final ByteData data = await rootBundle.load('lib/assets/images/hla.png');
    return data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
  }

  static Future<Uint8List> _getVLAImage() async {
    final ByteData data = await rootBundle.load('lib/assets/images/vla.png');
    return data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
  }

  static Future<Uint8List?> _getBackgroundImage(UserProfile userProfile) async {
    try {
      if (userProfile.toJson().containsKey('backgroundImagePath') &&
          userProfile.backgroundImagePath == null) {
        return Uint8List(0);
      }

      if (userProfile.backgroundImagePath != null) {
        final backgroundFile =
            await UserProfileService.getBackgroundImageFile();
        if (backgroundFile != null) {
          return backgroundFile.readAsBytesSync();
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error loading background image: $e');
      return null;
    }
  }

  static void _drawHeartSegmentChart(
    PdfPage page,
    Map<LVSegment, WallMotionScore> wallMotionScores,
    double x,
    double y,
    double size,
  ) {
    final Map<WallMotionScore, PdfColor> scoreColors = {
      WallMotionScore.normal: PdfColor(0, 204, 0),
      WallMotionScore.hypokinetic: PdfColor(255, 204, 0),
      WallMotionScore.akinetic: PdfColor(255, 102, 0),
      WallMotionScore.dyskinetic: PdfColor(165, 42, 42),
      WallMotionScore.aneurysmal: PdfColor(204, 0, 255),
    };

    final double centerX = x + size / 2;
    final double centerY = y + size / 2;

    final double apexRadius = size * 0.25 / 2;
    final double apicalRadius = size * 0.5 / 2;
    final double midRadius = size * 0.75 / 2;
    final double basalRadius = size / 2;

    final PdfColor apexColor =
        scoreColors[wallMotionScores[LVSegment.apex] ??
            WallMotionScore.normal]!;
    page.graphics.drawEllipse(
      Rect.fromCenter(
        center: Offset(centerX, centerY),
        width: apexRadius * 2,
        height: apexRadius * 2,
      ),
      brush: PdfSolidBrush(apexColor),
      pen: PdfPen(PdfColor(0, 0, 0), width: 0.5),
    );

    _drawSegment(
      page,
      centerX,
      centerY,
      apexRadius,
      apicalRadius,
      0,
      90,
      scoreColors[wallMotionScores[LVSegment.apicalAnterior] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      apexRadius,
      apicalRadius,
      90,
      180,
      scoreColors[wallMotionScores[LVSegment.apicalLateral] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      apexRadius,
      apicalRadius,
      180,
      270,
      scoreColors[wallMotionScores[LVSegment.apicalInferior] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      apexRadius,
      apicalRadius,
      270,
      360,
      scoreColors[wallMotionScores[LVSegment.apicalSeptal] ??
          WallMotionScore.normal]!,
    );

    _drawSegment(
      page,
      centerX,
      centerY,
      apicalRadius,
      midRadius,
      0,
      60,
      scoreColors[wallMotionScores[LVSegment.midAnterior] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      apicalRadius,
      midRadius,
      60,
      120,
      scoreColors[wallMotionScores[LVSegment.midAnterolateral] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      apicalRadius,
      midRadius,
      120,
      180,
      scoreColors[wallMotionScores[LVSegment.midInferolateral] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      apicalRadius,
      midRadius,
      180,
      240,
      scoreColors[wallMotionScores[LVSegment.midInferior] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      apicalRadius,
      midRadius,
      240,
      300,
      scoreColors[wallMotionScores[LVSegment.midInferoseptal] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      apicalRadius,
      midRadius,
      300,
      360,
      scoreColors[wallMotionScores[LVSegment.midAnteroseptal] ??
          WallMotionScore.normal]!,
    );

    _drawSegment(
      page,
      centerX,
      centerY,
      midRadius,
      basalRadius,
      0,
      60,
      scoreColors[wallMotionScores[LVSegment.basalAnterior] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      midRadius,
      basalRadius,
      60,
      120,
      scoreColors[wallMotionScores[LVSegment.basalAnterolateral] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      midRadius,
      basalRadius,
      120,
      180,
      scoreColors[wallMotionScores[LVSegment.basalInferolateral] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      midRadius,
      basalRadius,
      180,
      240,
      scoreColors[wallMotionScores[LVSegment.basalInferior] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      midRadius,
      basalRadius,
      240,
      300,
      scoreColors[wallMotionScores[LVSegment.basalInferoseptal] ??
          WallMotionScore.normal]!,
    );
    _drawSegment(
      page,
      centerX,
      centerY,
      midRadius,
      basalRadius,
      300,
      360,
      scoreColors[wallMotionScores[LVSegment.basalAnteroseptal] ??
          WallMotionScore.normal]!,
    );

    page.graphics.drawString(
      'LV 17-Segment Model',
      PdfStandardFont(PdfFontFamily.helvetica, 8, style: PdfFontStyle.bold),
      brush: PdfSolidBrush(PdfColor(0, 0, 0)),
      format: PdfStringFormat(alignment: PdfTextAlignment.center),
      bounds: Rect.fromLTWH(x, y - 15, size, 15),
    );
  }

  static void _drawHLAImageWithSegments(
    PdfPage page,
    Map<LVSegment, WallMotionScore> wallMotionScores,
    double x,
    double y,
    double width,
    double height,
    Uint8List hlaImageData,
    Uint8List notoFontData,
  ) {
    final coloredImageData = _colorHLASegments(hlaImageData, wallMotionScores);

    final PdfBitmap hlaImage = PdfBitmap(coloredImageData);

    page.graphics.save();

    page.graphics.setTransparency(1.0);

    page.graphics.drawImage(hlaImage, Rect.fromLTWH(x, y, width, height));

    page.graphics.restore();
  }

  static void _drawVLAImageWithSegments(
    PdfPage page,
    Map<LVSegment, WallMotionScore> wallMotionScores,
    double x,
    double y,
    double width,
    double height,
    Uint8List vlaImageData,
    Uint8List notoFontData,
  ) {
    final coloredImageData = _colorVLASegments(vlaImageData, wallMotionScores);

    final PdfBitmap vlaImage = PdfBitmap(coloredImageData);

    page.graphics.save();

    page.graphics.setTransparency(1.0);

    page.graphics.drawImage(vlaImage, Rect.fromLTWH(x, y, width, height));

    page.graphics.restore();
  }

  static Uint8List _colorHLASegments(
    Uint8List originalImageData,
    Map<LVSegment, WallMotionScore> wallMotionScores,
  ) {
    final Map<WallMotionScore, List<int>> scoreColors = {
      WallMotionScore.normal: [0, 204, 0],
      WallMotionScore.hypokinetic: [255, 204, 0],
      WallMotionScore.akinetic: [255, 102, 0],
      WallMotionScore.dyskinetic: [165, 42, 42],
      WallMotionScore.aneurysmal: [204, 0, 255],
    };

    final image = img.decodeImage(originalImageData);
    if (image == null) return originalImageData;

    final Map<LVSegment, List<int>> segmentSeedPoints = {
      LVSegment.basalInferoseptal: [351, 699],
      LVSegment.basalAnterolateral: [753, 649],
      LVSegment.midInferoseptal: [373, 476],
      LVSegment.midAnterolateral: [705, 457],
      LVSegment.apicalSeptal: [420, 268],
      LVSegment.apicalLateral: [664, 252],
      LVSegment.apex: [535, 82],
    };

    segmentSeedPoints.forEach((segment, seedPoint) {
      final score = wallMotionScores[segment] ?? WallMotionScore.normal;
      final color = scoreColors[score]!;

      _floodFillSegment(image, seedPoint[0], seedPoint[1], color);
    });

    return Uint8List.fromList(img.encodePng(image));
  }

  static Uint8List _colorVLASegments(
    Uint8List originalImageData,
    Map<LVSegment, WallMotionScore> wallMotionScores,
  ) {
    final Map<WallMotionScore, List<int>> scoreColors = {
      WallMotionScore.normal: [0, 204, 0],
      WallMotionScore.hypokinetic: [255, 204, 0],
      WallMotionScore.akinetic: [255, 102, 0],
      WallMotionScore.dyskinetic: [165, 42, 42],
      WallMotionScore.aneurysmal: [204, 0, 255],
    };

    final image = img.decodeImage(originalImageData);
    if (image == null) return originalImageData;

    final Map<LVSegment, List<int>> segmentSeedPoints = {
      LVSegment.basalAnterior: [785, 89],
      LVSegment.basalInferior: [777, 859],
      LVSegment.midAnterior: [1225, 122],
      LVSegment.midInferior: [1202, 774],
      LVSegment.apicalAnterior: [1549, 241],
      LVSegment.apicalInferior: [1552, 664],
      LVSegment.apex: [1731, 462],
    };

    segmentSeedPoints.forEach((segment, seedPoint) {
      final score = wallMotionScores[segment] ?? WallMotionScore.normal;
      final color = scoreColors[score]!;

      _floodFillSegment(image, seedPoint[0], seedPoint[1], color);
    });

    return Uint8List.fromList(img.encodePng(image));
  }

  static void _floodFillSegment(
    img.Image image,
    int startX,
    int startY,
    List<int> color,
  ) {
    if (startX < 0 ||
        startX >= image.width ||
        startY < 0 ||
        startY >= image.height) {
      return;
    }

    final targetPixel = image.getPixel(startX, startY);
    final targetR = targetPixel.r;
    final targetG = targetPixel.g;
    final targetB = targetPixel.b;
    final targetA = targetPixel.a;

    debugPrint(
      'Flood fill at ($startX, $startY): R=$targetR, G=$targetG, B=$targetB, A=$targetA',
    );

    final isGreen =
        targetR <= 5 && targetG >= 190 && targetG <= 205 && targetB <= 5;

    if (!isGreen) {
      debugPrint(
        'Skipping flood fill - pixel is not green #00C500 (R=$targetR, G=$targetG, B=$targetB)',
      );
      return;
    }

    final stack = <List<int>>[];
    final visited = <String>{};

    stack.add([startX, startY]);

    while (stack.isNotEmpty) {
      final current = stack.removeLast();
      final x = current[0];
      final y = current[1];

      if (x < 0 || x >= image.width || y < 0 || y >= image.height) {
        continue;
      }

      final key = '$x,$y';
      if (visited.contains(key)) {
        continue;
      }
      visited.add(key);

      final pixel = image.getPixel(x, y);
      final pixelR = pixel.r;
      final pixelG = pixel.g;
      final pixelB = pixel.b;

      final pixelIsGreen =
          pixelR <= 5 && pixelG >= 190 && pixelG <= 205 && pixelB <= 5;
      if (!pixelIsGreen) {
        continue;
      }

      image.setPixel(x, y, img.ColorRgba8(color[0], color[1], color[2], 255));

      stack.add([x + 1, y]);
      stack.add([x - 1, y]);
      stack.add([x, y + 1]);
      stack.add([x, y - 1]);
    }
  }

  static void _drawSegment(
    PdfPage page,
    double centerX,
    double centerY,
    double innerRadius,
    double outerRadius,
    double startAngle,
    double endAngle,
    PdfColor color,
  ) {
    final double startRad = startAngle * math.pi / 180;
    final double endRad = endAngle * math.pi / 180;

    final List<Offset> points = [];

    points.add(
      Offset(
        centerX + innerRadius * math.cos(startRad),
        centerY + innerRadius * math.sin(startRad),
      ),
    );

    points.add(
      Offset(
        centerX + outerRadius * math.cos(startRad),
        centerY + outerRadius * math.sin(startRad),
      ),
    );

    final int steps = 10;
    final double angleStep = (endRad - startRad) / steps;
    for (int i = 1; i <= steps; i++) {
      final double angle = startRad + i * angleStep;
      points.add(
        Offset(
          centerX + outerRadius * math.cos(angle),
          centerY + outerRadius * math.sin(angle),
        ),
      );
    }

    for (int i = steps; i >= 0; i--) {
      final double angle = startRad + i * angleStep;
      points.add(
        Offset(
          centerX + innerRadius * math.cos(angle),
          centerY + innerRadius * math.sin(angle),
        ),
      );
    }

    page.graphics.drawPolygon(
      points,
      brush: PdfSolidBrush(color),
      pen: PdfPen(PdfColor(0, 0, 0), width: 0.5),
    );
  }

  static Future<Map<String, dynamic>> _getPositionedBackgroundImages(
    UserProfile userProfile,
  ) async {
    Map<String, dynamic> positionedBackgrounds = {};

    try {
      for (final bgImage in userProfile.backgroundImages) {
        if (bgImage.position == 'center') {
          debugPrint(
            'Skipping center position background image during loading',
          );
          continue;
        }

        if (bgImage.type == 'color') {
          debugPrint(
            'Loading color background: position=${bgImage.position}, color=${bgImage.color}, alpha=${bgImage.alpha}',
          );
          positionedBackgrounds[bgImage.position] = {
            'type': 'color',
            'color': bgImage.color,
            'alpha': bgImage.alpha,
          };
          continue;
        }

        final file = await UserProfileService.getBackgroundImageFileByPath(
          bgImage.path,
        );
        if (file != null) {
          positionedBackgrounds[bgImage.position] = {
            'type': 'image',
            'data': file.readAsBytesSync(),
            'alpha': bgImage.alpha,
          };
        }
      }
    } catch (e) {
      debugPrint('Error loading positioned background images: $e');
    }

    return positionedBackgrounds;
  }

  static String _getAffectedCoronaryArteries(
    Map<LVSegment, WallMotionScore> wallMotionScores,
  ) {
    final Map<String, List<LVSegment>> coronaryTerritories = {
      'LAD': [
        LVSegment.basalAnterior,
        LVSegment.basalAnteroseptal,
        LVSegment.midAnterior,
        LVSegment.midAnteroseptal,
        LVSegment.apicalAnterior,
        LVSegment.apicalSeptal,
        LVSegment.apex,
      ],
      'LCX': [
        LVSegment.basalInferolateral,
        LVSegment.basalAnterolateral,
        LVSegment.midInferolateral,
        LVSegment.midAnterolateral,
        LVSegment.apicalLateral,
      ],
      'RCA': [
        LVSegment.basalInferior,
        LVSegment.basalInferoseptal,
        LVSegment.midInferior,
        LVSegment.midInferoseptal,
        LVSegment.apicalInferior,
      ],
    };

    final Set<String> affectedArteries = <String>{};

    coronaryTerritories.forEach((artery, segments) {
      for (final segment in segments) {
        final score = wallMotionScores[segment] ?? WallMotionScore.normal;
        if (score != WallMotionScore.normal) {
          affectedArteries.add(artery);
          break;
        }
      }
    });

    if (affectedArteries.isEmpty) {
      return '';
    }

    final List<String> arteryList = affectedArteries.toList()..sort();
    if (arteryList.length == 1) {
      return 'Affected: ${arteryList[0]}';
    } else if (arteryList.length == 2) {
      return 'Affected: ${arteryList.join(' & ')}';
    } else {
      return 'Affected: ${arteryList.sublist(0, arteryList.length - 1).join(', ')} & ${arteryList.last}';
    }
  }
}
