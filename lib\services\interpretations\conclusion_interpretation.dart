import 'package:flutter/foundation.dart';
import 'base_interpretation.dart';

class ConclusionInterpretation implements BaseInterpretation {
  static const double _normalEfLowerLimit = 55.0;
  static const double _mildlyReducedEfLowerLimit = 45.0;
  static const double _mildlyReducedEfUpperLimit = 54.0;

  static const double _normalLveddUpperLimitMale = 58.0;
  static const double _normalLveddUpperLimitFemale = 52.0;
  static const double _normalLvesdUpperLimitMale = 42.0;
  static const double _normalLvesdUpperLimitFemale = 38.0;

  static const double _normalIvsdUpperLimitMale = 10.0;
  static const double _normalIvsdUpperLimitFemale = 9.0;
  static const double _normalLvpwUpperLimitMale = 10.0;
  static const double _normalLvpwUpperLimitFemale = 9.0;

  static const double _normalRwtUpperLimit = 0.42;
  static const double _normalFsLowerLimit = 25.0;

  static const double _normalLaUpperLimit = 40.0;

  static const double _normalAoUpperLimit = 40.0;
  static const double _aorticAneurysmThreshold = 50.0;

  static const double _normalTapseLowerLimit = 16.0;
  static const double _normalRvBaseUpperLimit = 42.0;
  static const double _normalRvMidUpperLimit = 35.0;
  static const double _normalRvLengthUpperLimit = 86.0;
  static const double _normalRvWallThicknessUpperLimit = 5.0;

  static const double _normalIvcUpperLimit = 2.1;

  static const double _normalPaspUpperLimit = 30.0;

  static const double _moderatePaspLowerLimit = 46.0;
  static const double _severePaspLowerLimit = 61.0;

  @override
  String generateInterpretation(Map<String, dynamic> data) {
    final diagnoses = generateDiagnosesList(data);

    if (diagnoses.isEmpty) {
      return 'Normal cardiac structure and function.';
    }

    return diagnoses.join(', ');
  }

  /// Generates a list of individual diagnoses for bullet point formatting
  List<String> generateDiagnosesList(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return [];
    }

    final List<String> diagnoses = [];

    _checkForEmergentConditions(data, diagnoses);
    _checkForStructuralHeartDisease(data, diagnoses);
    _checkForHypertensiveHeartDisease(data, diagnoses);
    _checkForFunctionalAbnormalities(data, diagnoses);
    _checkForValvularDisease(data, diagnoses);
    _checkForOtherFindings(data, diagnoses);

    // Debug: Print diagnoses count and list
    debugPrint(
      'Conclusion Interpretation - Total diagnoses generated: ${diagnoses.length}',
    );
    for (int i = 0; i < diagnoses.length; i++) {
      debugPrint('Conclusion Interpretation - Diagnosis $i: ${diagnoses[i]}');
    }

    return diagnoses;
  }

  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String && value.trim().isNotEmpty) {
      try {
        return double.parse(value.trim());
      } catch (_) {
        return null;
      }
    }
    return null;
  }

  String _getGender(Map<String, dynamic> data) {
    final patientInfo = data['patientInfo'] ?? {};
    return (patientInfo['gender'] as String? ?? 'male').toLowerCase();
  }

  void _checkForEmergentConditions(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    _checkForCardiacTamponade(data, diagnoses);
    _checkForSevereAorticStenosis(data, diagnoses);
    _checkForAcuteAorticRegurgitation(data, diagnoses);
    _checkForSevereMitralRegurgitation(data, diagnoses);
  }

  void _checkForCardiacTamponade(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final pericardium = data['pericardium'] ?? {};
    final effusionSeverity =
        (pericardium['effusionSeverity'] as String? ?? 'none').toLowerCase();
    final unusualFindings = data['unusualFindings'] ?? {};
    final finding =
        (unusualFindings['finding'] as String? ?? 'none').toLowerCase();

    if (finding == 'cardiactamponade' ||
        (effusionSeverity == 'severe' && _hasHemodynamicCompromise(data))) {
      diagnoses.add('Cardiac tamponade');
    }
  }

  bool _hasHemodynamicCompromise(Map<String, dynamic> data) {
    final ivc = data['ivc'] ?? {};

    final ivcDiameter = _parseDouble(ivc['diameter']);
    final ivcCollapsibility =
        (ivc['collapsibility'] as String? ?? 'normal').toLowerCase();

    return (ivcDiameter != null && ivcDiameter > _normalIvcUpperLimit) &&
        (ivcCollapsibility == 'reduced' || ivcCollapsibility == 'absent');
  }

  void _checkForSevereAorticStenosis(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final valves = data['valves'] ?? {};
    final stenosis = valves['stenosis'] ?? {};
    final aorticStenosis =
        (stenosis['Aortic'] as String? ?? 'none').toLowerCase();

    if (aorticStenosis == 'severe') {
      diagnoses.add('Severe aortic stenosis');
    }
  }

  void _checkForAcuteAorticRegurgitation(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final valves = data['valves'] ?? {};
    final regurgitation = valves['regurgitation'] ?? {};
    final aorticRegurgitation =
        (regurgitation['Aortic'] as String? ?? 'none').toLowerCase();

    if (aorticRegurgitation == 'severe') {
      diagnoses.add('Severe aortic regurgitation');
    }
  }

  void _checkForSevereMitralRegurgitation(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final valves = data['valves'] ?? {};
    final regurgitation = valves['regurgitation'] ?? {};
    final mitralRegurgitation =
        (regurgitation['Mitral'] as String? ?? 'none').toLowerCase();

    if (mitralRegurgitation == 'severe') {
      diagnoses.add('Severe mitral regurgitation');
    }
  }

  void _checkForStructuralHeartDisease(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    _checkForLeftVentricularGeometry(data, diagnoses);
    _checkForCardiomyopathies(data, diagnoses);
    _checkForAorticPathology(data, diagnoses);
    _checkForCongenitalAbnormalities(data, diagnoses);
  }

  void _checkForLeftVentricularGeometry(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final echoParams = data['echoParameters'] ?? {};
    final ivsd = _parseDouble(echoParams['ivsd']);
    final lvpw = _parseDouble(echoParams['lvpw']);
    final relativeWallThickness = _parseDouble(
      echoParams['relativeWallThickness'],
    );
    final lvedd = _parseDouble(echoParams['lvedd']);
    final lvesd = _parseDouble(echoParams['lvesd']);
    final fs = _parseDouble(echoParams['fs']);
    final gender = _getGender(data);

    _checkForLvDimensions(lvedd, lvesd, gender, diagnoses);

    _checkForFractionalShortening(fs, diagnoses);

    if (ivsd == null || lvpw == null) return;

    final ivsdThreshold =
        gender == 'female'
            ? _normalIvsdUpperLimitFemale
            : _normalIvsdUpperLimitMale;
    final lvpwThreshold =
        gender == 'female'
            ? _normalLvpwUpperLimitFemale
            : _normalLvpwUpperLimitMale;
    final hasLvh = ivsd > ivsdThreshold || lvpw > lvpwThreshold;

    final hasMildWallThickening =
        (ivsd > ivsdThreshold && ivsd <= (ivsdThreshold + 3)) ||
        (lvpw > lvpwThreshold && lvpw <= (lvpwThreshold + 3));

    double? rwt = relativeWallThickness;
    if (rwt == null && lvedd != null) {
      rwt = (2 * lvpw) / lvedd;
    }

    if (hasLvh && rwt != null) {
      if (rwt > _normalRwtUpperLimit) {
        diagnoses.add('Concentric left ventricular hypertrophy');
      } else {
        diagnoses.add('Eccentric left ventricular hypertrophy');
      }
    } else if (rwt != null && rwt > _normalRwtUpperLimit) {
      diagnoses.add('Concentric remodeling');
    } else if (hasMildWallThickening) {
      diagnoses.add('Mild left ventricular wall thickening');
    }

    if (ivsd > 15 && ivsd > (lvpw * 1.3)) {
      diagnoses.add('Asymmetric septal hypertrophy');
    }
  }

  void _checkForLvDimensions(
    double? lvedd,
    double? lvesd,
    String gender,
    List<String> diagnoses,
  ) {
    final lveddThreshold =
        gender == 'female'
            ? _normalLveddUpperLimitFemale
            : _normalLveddUpperLimitMale;
    final lvesdThreshold =
        gender == 'female'
            ? _normalLvesdUpperLimitFemale
            : _normalLvesdUpperLimitMale;

    final bool hasLveddDilatation = lvedd != null && lvedd > lveddThreshold;
    final bool hasLvesdDilatation = lvesd != null && lvesd > lvesdThreshold;

    if (hasLveddDilatation || hasLvesdDilatation) {
      diagnoses.add('Left ventricular dilatation');
    }
  }

  void _checkForFractionalShortening(double? fs, List<String> diagnoses) {
    if (fs != null) {
      if (fs < _normalFsLowerLimit) {
        diagnoses.add('Reduced fractional shortening');
      }
    }
  }

  void _checkForHypertensiveHeartDisease(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final echoParams = data['echoParameters'] ?? {};
    final ivsd = _parseDouble(echoParams['ivsd']);
    final lvpw = _parseDouble(echoParams['lvpw']);
    final relativeWallThickness = _parseDouble(
      echoParams['relativeWallThickness'],
    );
    final lvedd = _parseDouble(echoParams['lvedd']);
    final la = _parseDouble(echoParams['la']);
    final diastolicGrade =
        (echoParams['diastolicGrade'] as String? ?? 'no').toLowerCase();
    final gender = _getGender(data);

    int hhdCriteria = 0;
    bool hasConcentricLvh = false;
    bool hasLaEnlargement = false;
    bool hasDiastolicDysfunction = false;

    if (ivsd != null && lvpw != null) {
      final ivsdThreshold =
          gender == 'female'
              ? _normalIvsdUpperLimitFemale
              : _normalIvsdUpperLimitMale;
      final lvpwThreshold =
          gender == 'female'
              ? _normalLvpwUpperLimitFemale
              : _normalLvpwUpperLimitMale;
      final hasLvh = ivsd > ivsdThreshold || lvpw > lvpwThreshold;

      if (hasLvh) {
        double? rwt = relativeWallThickness;
        if (rwt == null && lvedd != null) {
          rwt = (2 * lvpw) / lvedd;
        }

        if (rwt != null && rwt > _normalRwtUpperLimit) {
          hasConcentricLvh = true;
          hhdCriteria++;
        }
      }
    }

    if (la != null && la > _normalLaUpperLimit) {
      hasLaEnlargement = true;
      hhdCriteria++;
    }

    if (diastolicGrade == 'grade i' ||
        diastolicGrade == 'grade ii' ||
        diastolicGrade == 'grade iii' ||
        diastolicGrade == 'grade iv') {
      hasDiastolicDysfunction = true;
      hhdCriteria++;
    }

    if (hhdCriteria >= 2 && hasConcentricLvh) {
      diagnoses.add('Hypertensive heart disease');
    } else if (hasConcentricLvh &&
        (hasLaEnlargement || hasDiastolicDysfunction)) {
      diagnoses.add('Hypertensive heart disease');
    }
  }

  void _checkForCardiomyopathies(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final echoParams = data['echoParameters'] ?? {};
    final ef = _parseDouble(echoParams['ef']);
    final lvedd = _parseDouble(echoParams['lvedd']);
    final unusualFindings = data['unusualFindings'] ?? {};
    final finding =
        (unusualFindings['finding'] as String? ?? 'none').toLowerCase();

    if (ef != null &&
        ef < _mildlyReducedEfLowerLimit &&
        lvedd != null &&
        lvedd > 59) {
      diagnoses.add('Dilated cardiomyopathy');
      return;
    }

    if (finding == 'hypertrophicobstruction') {
      diagnoses.add('Hypertrophic obstructive cardiomyopathy');
      return;
    }

    _checkForWallMotionAbnormalities(data, diagnoses);
  }

  void _checkForWallMotionAbnormalities(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final wallMotion = data['wallMotion'] ?? {};

    int abnormalSegments = 0;
    for (int i = 1; i <= 17; i++) {
      final segmentKey = 'segment$i';
      final segmentValue = wallMotion[segmentKey];
      if (segmentValue != null &&
          segmentValue.toString().toLowerCase() != 'normal') {
        abnormalSegments++;
      }
    }

    final echoParams = data['echoParameters'] ?? {};
    final ef = _parseDouble(echoParams['ef']);

    if (abnormalSegments > 0) {
      if (abnormalSegments >= 10) {
        if (ef != null && ef < _mildlyReducedEfLowerLimit) {
          diagnoses.add('Global hypokinesia');
        }
      } else if (abnormalSegments >= 2) {
        diagnoses.add('Ischemic heart disease');
      }
    }
  }

  void _checkForAorticPathology(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final echoParams = data['echoParameters'] ?? {};
    final ao = _parseDouble(echoParams['ao']);
    final unusualFindings = data['unusualFindings'] ?? {};
    final finding =
        (unusualFindings['finding'] as String? ?? 'none').toLowerCase();

    if (ao != null) {
      if (ao > _aorticAneurysmThreshold) {
        diagnoses.add('Aortic aneurysm');
      } else if (ao > _normalAoUpperLimit) {
        diagnoses.add('Aortic dilatation');
      }
    }

    if (finding.contains('dissection') ||
        finding == 'flap' ||
        finding == 'intimaltear') {
      diagnoses.add('Suspected aortic dissection');
    }
  }

  void _checkForCongenitalAbnormalities(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final unusualFindings = data['unusualFindings'] ?? {};
    final septalDeformity =
        (unusualFindings['septalDeformity'] as String? ?? 'none').toLowerCase();

    switch (septalDeformity) {
      case 'hypertrophy':
        diagnoses.add(
          'Septal hypertrophy (increased wall thickness suggesting pressure overload or cardiomyopathy)',
        );
        break;
      case 'flattening':
        diagnoses.add(
          'Septal flattening (indicates right ventricular pressure or volume overload)',
        );
        break;
      case 'bowing':
        diagnoses.add(
          'Septal bowing (suggests pressure differential between ventricles)',
        );
        break;
      case 'aneurysm':
        diagnoses.add(
          'Septal aneurysm (localized bulging requiring monitoring for complications)',
        );
        break;
      case 'vsd':
        diagnoses.add(
          'Ventricular septal defect (congenital heart defect requiring hemodynamic assessment)',
        );
        break;
      case 'righttoleftshunt':
        diagnoses.add(
          'Right-to-left shunt (indicates elevated right heart pressures with potential cyanosis)',
        );
        break;
    }
  }

  void _checkForFunctionalAbnormalities(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    _checkForHeartFailure(data, diagnoses);
    _checkForDiastolicDysfunction(data, diagnoses);
    _checkForRightHeartAbnormalities(data, diagnoses);
    _checkForPulmonaryHypertension(data, diagnoses);
  }

  void _checkForHeartFailure(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final echoParams = data['echoParameters'] ?? {};
    final ef = _parseDouble(echoParams['ef']);
    final la = _parseDouble(echoParams['la']);
    final diastolicGrade =
        (echoParams['diastolicGrade'] as String? ?? 'no').toLowerCase();

    if (ef == null) return;

    if (ef < _normalEfLowerLimit && ef > _mildlyReducedEfUpperLimit) {
      diagnoses.add('Mildly reduced left ventricular ejection fraction');
    }

    if (ef < _mildlyReducedEfLowerLimit) {
      diagnoses.add('Heart failure with reduced ejection fraction (HFrEF)');
    } else if (ef >= _mildlyReducedEfLowerLimit &&
        ef <= _mildlyReducedEfUpperLimit) {
      diagnoses.add(
        'Heart failure with mildly reduced ejection fraction (HFmrEF)',
      );
    } else if (ef >= _normalEfLowerLimit) {
      if ((la != null && la > _normalLaUpperLimit) ||
          (diastolicGrade == 'grade ii' ||
              diastolicGrade == 'grade iii' ||
              diastolicGrade == 'grade iv')) {
        diagnoses.add('Heart failure with preserved ejection fraction (HFpEF)');
      }
    }
  }

  void _checkForDiastolicDysfunction(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final echoParams = data['echoParameters'] ?? {};
    final diastolicGrade =
        (echoParams['diastolicGrade'] as String? ?? 'no').toLowerCase();

    switch (diastolicGrade) {
      case 'grade i':
        diagnoses.add('Grade I diastolic dysfunction (impaired relaxation)');
        break;
      case 'grade ii':
        diagnoses.add('Grade II diastolic dysfunction (pseudonormal filling)');
        break;
      case 'grade iii':
        diagnoses.add('Grade III diastolic dysfunction (restrictive filling)');
        break;
      case 'grade iv':
        diagnoses.add(
          'Grade IV diastolic dysfunction (fixed restrictive filling)',
        );
        break;
    }
  }

  void _checkForRightHeartAbnormalities(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final echoParams = data['echoParameters'] ?? {};
    final tapse = _parseDouble(echoParams['tapse']);
    final rvSize = (echoParams['rvSize'] as String? ?? 'normal').toLowerCase();
    final raSize = (echoParams['raSize'] as String? ?? 'normal').toLowerCase();

    final rvBase = _parseDouble(echoParams['rvBase']);
    final rvMid = _parseDouble(echoParams['rvMid']);
    final rvLength = _parseDouble(echoParams['rvLength']);
    final rvWallThickness = _parseDouble(echoParams['rvWallThickness']);

    final ivc = data['ivc'] ?? {};
    final ivcDiameter = _parseDouble(ivc['diameter']);
    final ivcCollapsibility =
        (ivc['collapsibility'] as String? ?? 'normal').toLowerCase();

    _checkForRvEnlargement(rvBase, rvMid, rvLength, rvSize, diagnoses);

    _checkForRvHypertrophy(rvWallThickness, diagnoses);

    if (tapse != null && tapse < _normalTapseLowerLimit) {
      diagnoses.add('Right ventricular dysfunction');
    }

    _checkForRightHeartFailure(
      tapse,
      rvSize,
      raSize,
      ivcDiameter,
      ivcCollapsibility,
      diagnoses,
    );

    if (raSize == 'dilated' &&
        !diagnoses.any((d) => d.contains('Right heart failure'))) {
      diagnoses.add('Right atrial enlargement');
    }
  }

  void _checkForRvEnlargement(
    double? rvBase,
    double? rvMid,
    double? rvLength,
    String rvSize,
    List<String> diagnoses,
  ) {
    bool hasQuantitativeEnlargement = false;

    if (rvBase != null && rvBase > _normalRvBaseUpperLimit) {
      hasQuantitativeEnlargement = true;
    }
    if (rvMid != null && rvMid > _normalRvMidUpperLimit) {
      hasQuantitativeEnlargement = true;
    }
    if (rvLength != null && rvLength > _normalRvLengthUpperLimit) {
      hasQuantitativeEnlargement = true;
    }

    if (hasQuantitativeEnlargement || rvSize == 'dilated') {
      diagnoses.add('Right ventricular enlargement');
    }
  }

  void _checkForRvHypertrophy(double? rvWallThickness, List<String> diagnoses) {
    if (rvWallThickness != null &&
        rvWallThickness > _normalRvWallThicknessUpperLimit) {
      diagnoses.add('Right ventricular hypertrophy');
    }
  }

  void _checkForRightHeartFailure(
    double? tapse,
    String rvSize,
    String raSize,
    double? ivcDiameter,
    String ivcCollapsibility,
    List<String> diagnoses,
  ) {
    int rhfCriteria = 0;

    if (tapse != null && tapse < _normalTapseLowerLimit) {
      rhfCriteria++;
    }

    if (rvSize == 'dilated') {
      rhfCriteria++;
    }

    if (raSize == 'dilated') {
      rhfCriteria++;
    }

    if (ivcDiameter != null &&
        ivcDiameter > _normalIvcUpperLimit &&
        (ivcCollapsibility == 'reduced' || ivcCollapsibility == 'absent')) {
      rhfCriteria++;
    }

    if (rhfCriteria >= 2) {
      diagnoses.add('Right heart failure');
    }
  }

  void _checkForPulmonaryHypertension(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final echoParams = data['echoParameters'] ?? {};
    final pasp = _parseDouble(echoParams['pasp']);

    if (pasp != null) {
      if (pasp >= _severePaspLowerLimit) {
        diagnoses.add('Severe pulmonary hypertension');
      } else if (pasp >= _moderatePaspLowerLimit) {
        diagnoses.add('Moderate pulmonary hypertension');
      } else if (pasp > _normalPaspUpperLimit) {
        diagnoses.add('Mild pulmonary hypertension');
      }
    }
  }

  void _checkForValvularDisease(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final valves = data['valves'] ?? {};
    final stenosis = valves['stenosis'] ?? {};
    final regurgitation = valves['regurgitation'] ?? {};
    final morphology = valves['morphology'] ?? {};

    _checkValveMorphology(morphology, diagnoses);

    _checkProstheticValves(valves, diagnoses);

    _checkValveDisease('Mitral', stenosis, regurgitation, diagnoses);
    _checkValveDisease('Aortic', stenosis, regurgitation, diagnoses);
    _checkValveDisease('Tricuspid', stenosis, regurgitation, diagnoses);
    _checkValveDisease('Pulmonary', stenosis, regurgitation, diagnoses);
  }

  void _checkValveMorphology(
    Map<String, dynamic> morphology,
    List<String> diagnoses,
  ) {
    morphology.forEach((valveName, morphologyType) {
      if (morphologyType == null ||
          morphologyType.toString().toLowerCase() == 'normal') {
        return;
      }

      final morphologyStr = morphologyType.toString().toLowerCase();
      final valveNameLower = valveName.toString().toLowerCase();

      switch (morphologyStr) {
        case 'bicuspid':
          diagnoses.add(
            'Bicuspid $valveNameLower valve (congenital abnormality with increased risk of stenosis and regurgitation)',
          );
          break;
        case 'calcified':
          diagnoses.add(
            'Calcified $valveNameLower valve (degenerative process causing stenosis and/or regurgitation)',
          );
          break;
        case 'thickened':
          diagnoses.add(
            'Thickened $valveNameLower valve (may progress to stenosis or regurgitation)',
          );
          break;
        case 'prolapse':
          diagnoses.add(
            '${valveNameLower.substring(0, 1).toUpperCase()}${valveNameLower.substring(1)} valve prolapse (leaflet displacement causing regurgitation)',
          );
          break;
        case 'flail':
          diagnoses.add(
            'Flail $valveNameLower valve (severe leaflet dysfunction with significant regurgitation)',
          );
          break;
        case 'rheumatic':
          diagnoses.add(
            'Rheumatic $valveNameLower valve disease (post-inflammatory changes causing stenosis and/or regurgitation)',
          );
          break;
        case 'myxomatous':
          diagnoses.add(
            'Myxomatous $valveNameLower valve disease (degenerative process with increased regurgitation risk)',
          );
          break;
        case 'degenerative':
          diagnoses.add(
            'Degenerative $valveNameLower valve disease (age-related changes with functional impairment)',
          );
          break;
        case 'thrombus':
          diagnoses.add(
            '$valveNameLower valve thrombus (requires anticoagulation and urgent evaluation)',
          );
          break;
        case 'vegetation':
          diagnoses.add(
            '$valveNameLower valve vegetation (suggests infective endocarditis requiring urgent treatment)',
          );
          break;
        case 'prosthetic':
          break;
        default:
          diagnoses.add(
            '${valveNameLower.substring(0, 1).toUpperCase()}${valveNameLower.substring(1)} valve abnormality: $morphologyStr',
          );
          break;
      }
    });
  }

  void _checkProstheticValves(
    Map<String, dynamic> valves,
    List<String> diagnoses,
  ) {
    final prosthetic = valves['prosthetic'] ?? {};
    final prostheticTypes = valves['prostheticTypes'] ?? {};
    final prostheticFunctions = valves['prostheticFunctions'] ?? {};

    for (final valve in ['mitral', 'aortic', 'tricuspid', 'pulmonary']) {
      final isProsthetic = prosthetic[valve] == true;
      if (!isProsthetic) continue;

      final valveType = prostheticTypes[valve] as String?;
      final valveFunction = prostheticFunctions[valve] as String?;

      if (valveFunction != null && valveFunction.toLowerCase() != 'normal') {
        final valveName =
            valve.substring(0, 1).toUpperCase() + valve.substring(1);
        final typeStr = valveType != null ? ' ($valveType)' : '';

        switch (valveFunction.toLowerCase()) {
          case 'svdleafletcalcification':
            diagnoses.add(
              '$valveName prosthetic valve$typeStr dysfunction (leaflet calcification)',
            );
            break;
          case 'svdleaflettear':
            diagnoses.add(
              '$valveName prosthetic valve$typeStr dysfunction (leaflet tear)',
            );
            break;
          case 'svdstentfracture':
            diagnoses.add(
              '$valveName prosthetic valve$typeStr dysfunction (stent fracture)',
            );
            break;
          case 'ppm':
            diagnoses.add('$valveName prosthesis-patient mismatch$typeStr');
            break;
          case 'paravalvularleak':
            diagnoses.add(
              '$valveName prosthetic valve$typeStr paravalvular leak',
            );
            break;
          case 'pannus':
            diagnoses.add(
              '$valveName prosthetic valve$typeStr pannus formation',
            );
            break;
          case 'malposition':
            diagnoses.add('$valveName prosthetic valve$typeStr malposition');
            break;
          case 'endocarditis':
            diagnoses.add('$valveName prosthetic valve$typeStr endocarditis');
            break;
          case 'thrombus':
            diagnoses.add('$valveName prosthetic valve$typeStr thrombus');
            break;
          default:
            diagnoses.add('$valveName prosthetic valve$typeStr dysfunction');
            break;
        }
      }
    }
  }

  void _checkValveDisease(
    String valveName,
    Map<String, dynamic> stenosis,
    Map<String, dynamic> regurgitation,
    List<String> diagnoses,
  ) {
    final stenosisGrade =
        (stenosis[valveName] as String? ?? 'none').toLowerCase();
    final regurgitationGrade =
        (regurgitation[valveName] as String? ?? 'none').toLowerCase();

    if (stenosisGrade != 'none') {
      switch (stenosisGrade) {
        case 'severe':
          diagnoses.add('Severe ${valveName.toLowerCase()} stenosis');
          break;
        case 'moderate':
          diagnoses.add('Moderate ${valveName.toLowerCase()} stenosis');
          break;
        case 'mild':
          diagnoses.add('Mild ${valveName.toLowerCase()} stenosis');
          break;
      }
    }

    if (regurgitationGrade != 'none') {
      switch (regurgitationGrade) {
        case 'severe':
          diagnoses.add('Severe ${valveName.toLowerCase()} regurgitation');
          break;
        case 'moderate':
          diagnoses.add('Moderate ${valveName.toLowerCase()} regurgitation');
          break;
        case 'mild':
          diagnoses.add('Mild ${valveName.toLowerCase()} regurgitation');
          break;
      }
    }
  }

  void _checkForOtherFindings(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    _checkForPericardialDisease(data, diagnoses);
    _checkForUnusualFindings(data, diagnoses);
  }

  void _checkForPericardialDisease(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final pericardium = data['pericardium'] ?? {};
    final effusionSeverity =
        (pericardium['effusionSeverity'] as String? ?? 'none').toLowerCase();
    final effusionLocation =
        (pericardium['effusionLocation'] as String? ?? '').toLowerCase();

    if (effusionSeverity != 'none') {
      String locationText = '';
      if (effusionLocation.isNotEmpty && effusionLocation != 'none') {
        locationText = '$effusionLocation ';
      }

      switch (effusionSeverity) {
        case 'severe':
          diagnoses.add('Severe ${locationText}pericardial effusion');
          break;
        case 'moderate':
          diagnoses.add('Moderate ${locationText}pericardial effusion');
          break;
        case 'mild':
          diagnoses.add('Mild ${locationText}pericardial effusion');
          break;
        case 'trace':
          diagnoses.add('Trace ${locationText}pericardial effusion');
          break;
      }
    }
  }

  void _checkForUnusualFindings(
    Map<String, dynamic> data,
    List<String> diagnoses,
  ) {
    final unusualFindings = data['unusualFindings'] ?? {};
    final finding =
        (unusualFindings['finding'] as String? ?? 'none').toLowerCase();
    final description = unusualFindings['description'] as String? ?? '';

    if (finding == 'none') return;

    switch (finding) {
      case 'mass':
        diagnoses.add(
          'Cardiac mass (requires further imaging and possible biopsy for characterization)',
        );
        break;
      case 'thrombus':
        diagnoses.add(
          'Intracardiac thrombus (requires urgent anticoagulation and embolic risk assessment)',
        );
        break;
      case 'vegetation':
        diagnoses.add(
          'Vegetation (suggests infective endocarditis requiring blood cultures and antibiotics)',
        );
        break;
      case 'rupture':
        diagnoses.add(
          'Cardiac rupture (life-threatening emergency requiring immediate surgical intervention)',
        );
        break;
      case 'pseudoaneurysm':
        diagnoses.add(
          'Pseudoaneurysm (contained rupture requiring urgent surgical evaluation)',
        );
        break;
      case 'dissection':
        diagnoses.add(
          'Suspected aortic dissection (medical emergency requiring immediate imaging and surgical consultation)',
        );
        break;
      case 'flap':
        diagnoses.add(
          'Intimal flap (suggests aortic dissection requiring urgent evaluation)',
        );
        break;
      case 'intimaltear':
        diagnoses.add(
          'Intimal tear (aortic injury requiring monitoring for progression to dissection)',
        );
        break;
      case 'apicalballooning':
        diagnoses.add(
          'Apical ballooning (stress-induced cardiomyopathy with reversible dysfunction)',
        );
        break;
      case 'takotsubo':
        diagnoses.add(
          'Takotsubo cardiomyopathy (stress-induced reversible cardiomyopathy requiring supportive care)',
        );
        break;
      case 'cardiactamponade':
        diagnoses.add(
          'Cardiac tamponade (life-threatening emergency requiring immediate pericardiocentesis)',
        );
        break;
      case 'restrictivephysiology':
        diagnoses.add(
          'Restrictive physiology (impaired ventricular filling requiring hemodynamic optimization)',
        );
        break;
      case 'constrictivepericarditis':
        diagnoses.add(
          'Constrictive pericarditis (pericardial scarring causing impaired cardiac filling)',
        );
        break;
      case 'hypertrophicobstruction':
        diagnoses.add(
          'Hypertrophic obstructive cardiomyopathy (dynamic outflow obstruction requiring specialized management)',
        );
        break;
      case 'cardiacamyloidosis':
        diagnoses.add(
          'Cardiac amyloidosis (infiltrative disease requiring systemic evaluation and specialized treatment)',
        );
        break;
      case 'cardiacsarcoidosis':
        diagnoses.add(
          'Cardiac sarcoidosis (inflammatory infiltrative disease requiring immunosuppression and arrhythmia monitoring)',
        );
        break;
      case 'cardiactumor':
        diagnoses.add(
          'Cardiac tumor (requires histological diagnosis and multidisciplinary oncology consultation)',
        );
        break;
      case 'cardiacmetastasis':
        diagnoses.add(
          'Cardiac metastasis (secondary malignancy requiring staging and oncology management)',
        );
        break;
      case 'pulmonaryembolism':
        diagnoses.add(
          'Pulmonary embolism (requires immediate anticoagulation and risk stratification)',
        );
        break;
      case 'infectiveendocarditis':
        diagnoses.add(
          'Infective endocarditis (requires blood cultures, prolonged antibiotics, and surgical evaluation)',
        );
        break;
      case 'other':
        if (description.isNotEmpty) {
          diagnoses.add('Unusual finding: $description');
        } else {
          diagnoses.add('Unusual cardiac finding');
        }
        break;
      default:
        diagnoses.add(
          'Unusual finding: ${finding.replaceAll(RegExp(r'([a-z])([A-Z])'), r'$1 $2').toLowerCase()}',
        );
        break;
    }
  }
}
